{"version": "3.1.4", "results": [[":tests/responses-chat-completions.test.ts", {"duration": 580.222154, "failed": false}], [":tests/history-overlay.test.tsx", {"duration": 234.83553500000016, "failed": false}], [":tests/config.test.tsx", {"duration": 10.333584999999857, "failed": false}], [":tests/text-buffer.test.ts", {"duration": 24.79697000000033, "failed": false}], [":tests/text-buffer-gaps.test.ts", {"duration": 37.72177399999964, "failed": false}], [":tests/apply-patch.test.ts", {"duration": 11.986867000000075, "failed": false}], [":tests/file-tag-utils.test.ts", {"duration": 14.269960999999967, "failed": false}], [":tests/multiline-history-behavior.test.tsx", {"duration": 122.69541299999946, "failed": false}], [":tests/terminal-chat-input-file-tag-suggestions.test.tsx", {"duration": 150.56669200000033, "failed": false}], [":tests/check-updates.test.ts", {"duration": 157.91511300000002, "failed": false}], [":tests/markdown.test.tsx", {"duration": 89.04115499999898, "failed": true}], [":tests/approvals.test.ts", {"duration": 11.866485000000011, "failed": false}], [":tests/agent-thinking-time.test.ts", {"duration": 28.631468999999925, "failed": false}], [":tests/agent-terminate.test.ts", {"duration": 90.8754370000006, "failed": false}], [":tests/agent-network-errors.test.ts", {"duration": 75.45296899999994, "failed": false}], [":tests/agent-cancel.test.ts", {"duration": 212.26330099999996, "failed": false}], [":tests/agent-project-doc.test.ts", {"duration": 20.269665000000714, "failed": false}], [":tests/agent-function-call-id.test.ts", {"duration": 79.21893699999964, "failed": false}], [":tests/agent-server-retry.test.ts", {"duration": 73.48201000000063, "failed": false}], [":tests/text-buffer-word.test.ts", {"duration": 17.344591000000037, "failed": false}], [":tests/agent-rate-limit-error.test.ts", {"duration": 22.02733199999966, "failed": false}], [":tests/agent-cancel-race.test.ts", {"duration": 65.18587699999989, "failed": false}], [":tests/agent-cancel-prev-response.test.ts", {"duration": 64.13507200000004, "failed": false}], [":tests/terminal-chat-model-selection.test.tsx", {"duration": 169.94221900000048, "failed": false}], [":tests/agent-interrupt-continue.test.ts", {"duration": 20.138982000000397, "failed": false}], [":tests/terminal-chat-input-multiline.test.tsx", {"duration": 113.55511000000115, "failed": false}], [":tests/agent-generic-network-error.test.ts", {"duration": 89.3237079999999, "failed": false}], [":tests/config_reasoning.test.ts", {"duration": 11.210015999999996, "failed": false}], [":tests/clear-command.test.tsx", {"duration": 47.85997099999986, "failed": false}], [":tests/agent-dedupe-items.test.ts", {"duration": 38.844232000000375, "failed": false}], [":tests/raw-exec-process-group.test.ts", {"duration": 117.03429300000016, "failed": false}], [":tests/token-streaming-performance.test.ts", {"duration": 22.424388000000363, "failed": false}], [":tests/agent-cancel-early.test.ts", {"duration": 31.65748799999983, "failed": false}], [":tests/multiline-dynamic-width.test.tsx", {"duration": 31.41354400000091, "failed": false}], [":tests/disableResponseStorage.agentLoop.test.ts", {"duration": 33.157656999999745, "failed": false}], [":tests/model-utils.test.ts", {"duration": 10.441227999999683, "failed": false}], [":tests/agent-max-tokens-error.test.ts", {"duration": 36.14852599999995, "failed": false}], [":tests/typeahead-scroll.test.tsx", {"duration": 27.821921000000657, "failed": false}], [":tests/file-system-suggestions.test.ts", {"duration": 8.696167999999943, "failed": false}], [":tests/invalid-command-handling.test.ts", {"duration": 450.3871369999997, "failed": false}], [":tests/agent-invalid-request-error.test.ts", {"duration": 42.24104999999963, "failed": false}], [":tests/package-manager-detector.test.ts", {"duration": 4.026816999999937, "failed": false}], [":tests/model-utils-network-error.test.ts", {"duration": 630.8926190000002, "failed": false}], [":tests/create-truncating-collector.test.ts", {"duration": 7.065685999999914, "failed": false}], [":tests/cancel-exec.test.ts", {"duration": 47.73633800000016, "failed": false}], [":tests/project-doc.test.ts", {"duration": 10.303224000000228, "failed": false}], [":tests/multiline-newline.test.tsx", {"duration": 37.919590999999855, "failed": false}], [":tests/terminal-chat-completions.test.tsx", {"duration": 30.018991999999344, "failed": false}], [":tests/user-config-env.test.ts", {"duration": 228.16865000000007, "failed": false}], [":tests/text-buffer-copy-paste.test.ts", {"duration": 18.38935300000003, "failed": false}], [":tests/exec-apply-patch.test.ts", {"duration": 7.6435529999998835, "failed": false}], [":tests/requires-shell.test.ts", {"duration": 5.363678000000164, "failed": false}], [":tests/input-utils.test.ts", {"duration": 12.249409999999898, "failed": false}], [":tests/terminal-chat-response-item.test.tsx", {"duration": 30.521805000000313, "failed": false}], [":tests/multiline-shift-enter-crlf.test.tsx", {"duration": 35.701953999999205, "failed": false}], [":tests/disableResponseStorage.test.ts", {"duration": 43.10448200000019, "failed": false}], [":tests/slash-commands.test.ts", {"duration": 5.000683000000208, "failed": false}], [":tests/multiline-shift-enter-mod1.test.tsx", {"duration": 32.87074399999983, "failed": false}], [":tests/fixed-requires-shell.test.ts", {"duration": 3.8907140000001164, "failed": false}], [":tests/parse-apply-patch.test.ts", {"duration": 4.764808000000357, "failed": false}], [":tests/multiline-shift-enter.test.tsx", {"duration": 28.93022799999926, "failed": false}], [":tests/terminal-chat-input-compact.test.tsx", {"duration": 26.297207999999955, "failed": false}], [":tests/multiline-ctrl-enter-submit.test.tsx", {"duration": 36.12397299999975, "failed": false}], [":tests/api-key.test.ts", {"duration": 252.32270800000015, "failed": false}], [":tests/multiline-enter-submit-cr.test.tsx", {"duration": 25.04261299999962, "failed": false}], [":tests/get-diff-special-chars.test.ts", {"duration": 104.3138100000001, "failed": false}], [":tests/format-command.test.ts", {"duration": 4.13827399999991, "failed": false}], [":tests/model-info.test.ts", {"duration": 7.160751999999775, "failed": false}], [":tests/pipe-command.test.ts", {"duration": 3.9845290000002933, "failed": false}], [":tests/text-buffer-crlf.test.ts", {"duration": 12.589541000000281, "failed": false}], [":tests/dummy.test.ts", {"duration": 2.753222000000278, "failed": false}]]}