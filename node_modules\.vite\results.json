{"version": "3.1.4", "results": [[":tests/responses-chat-completions.test.ts", {"duration": 297.4185, "failed": false}], [":tests/history-overlay.test.tsx", {"duration": 233.8016, "failed": false}], [":tests/config.test.tsx", {"duration": 20.5621000000001, "failed": false}], [":tests/text-buffer.test.ts", {"duration": 59.202600000000075, "failed": false}], [":tests/text-buffer-gaps.test.ts", {"duration": 85.99810000000002, "failed": false}], [":tests/apply-patch.test.ts", {"duration": 31.17560000000003, "failed": false}], [":tests/file-tag-utils.test.ts", {"duration": 63.342199999999934, "failed": false}], [":tests/multiline-history-behavior.test.tsx", {"duration": 233.39010000000007, "failed": false}], [":tests/terminal-chat-input-file-tag-suggestions.test.tsx", {"duration": 444.83849999999984, "failed": false}], [":tests/check-updates.test.ts", {"duration": 75.31669999999986, "failed": false}], [":tests/markdown.test.tsx", {"duration": 283.52340000000004, "failed": true}], [":tests/approvals.test.ts", {"duration": 19.48429999999985, "failed": false}], [":tests/agent-thinking-time.test.ts", {"duration": 37.20659999999998, "failed": false}], [":tests/agent-terminate.test.ts", {"duration": 100.3596, "failed": false}], [":tests/agent-network-errors.test.ts", {"duration": 73.43099999999981, "failed": false}], [":tests/agent-cancel.test.ts", {"duration": 227.95669999999996, "failed": false}], [":tests/agent-project-doc.test.ts", {"duration": 19.19959999999992, "failed": false}], [":tests/agent-function-call-id.test.ts", {"duration": 57.849500000000035, "failed": false}], [":tests/agent-server-retry.test.ts", {"duration": 69.11779999999999, "failed": false}], [":tests/text-buffer-word.test.ts", {"duration": 39.35770000000002, "failed": false}], [":tests/agent-rate-limit-error.test.ts", {"duration": 19.705799999999954, "failed": false}], [":tests/agent-cancel-race.test.ts", {"duration": 74.40339999999992, "failed": false}], [":tests/agent-cancel-prev-response.test.ts", {"duration": 61.580400000000054, "failed": false}], [":tests/terminal-chat-model-selection.test.tsx", {"duration": 151.88839999999982, "failed": false}], [":tests/agent-interrupt-continue.test.ts", {"duration": 19.701000000000022, "failed": false}], [":tests/terminal-chat-input-multiline.test.tsx", {"duration": 201.06839999999988, "failed": false}], [":tests/agent-generic-network-error.test.ts", {"duration": 86.63149999999996, "failed": false}], [":tests/config_reasoning.test.ts", {"duration": 16.661500000000046, "failed": false}], [":tests/clear-command.test.tsx", {"duration": 115.08680000000004, "failed": false}], [":tests/agent-dedupe-items.test.ts", {"duration": 49.28880000000004, "failed": false}], [":tests/raw-exec-process-group.test.ts", {"duration": 5.402699999999982, "failed": false}], [":tests/token-streaming-performance.test.ts", {"duration": 24.730800000000045, "failed": false}], [":tests/agent-cancel-early.test.ts", {"duration": 36.29840000000013, "failed": false}], [":tests/multiline-dynamic-width.test.tsx", {"duration": 97.93779999999992, "failed": false}], [":tests/disableResponseStorage.agentLoop.test.ts", {"duration": 17.600999999999885, "failed": false}], [":tests/model-utils.test.ts", {"duration": 17.77710000000002, "failed": false}], [":tests/agent-max-tokens-error.test.ts", {"duration": 38.0453, "failed": false}], [":tests/typeahead-scroll.test.tsx", {"duration": 71.79930000000013, "failed": false}], [":tests/file-system-suggestions.test.ts", {"duration": 33.30419999999992, "failed": true}], [":tests/invalid-command-handling.test.ts", {"duration": 21.67470000000003, "failed": false}], [":tests/agent-invalid-request-error.test.ts", {"duration": 48.92520000000013, "failed": false}], [":tests/package-manager-detector.test.ts", {"duration": 21.174499999999966, "failed": true}], [":tests/model-utils-network-error.test.ts", {"duration": 138.09849999999994, "failed": false}], [":tests/create-truncating-collector.test.ts", {"duration": 13.691599999999994, "failed": false}], [":tests/cancel-exec.test.ts", {"duration": 159.8895, "failed": false}], [":tests/project-doc.test.ts", {"duration": 37.49059999999997, "failed": false}], [":tests/multiline-newline.test.tsx", {"duration": 80.90520000000015, "failed": false}], [":tests/terminal-chat-completions.test.tsx", {"duration": 51.05840000000012, "failed": false}], [":tests/user-config-env.test.ts", {"duration": 66.13699999999994, "failed": true}], [":tests/text-buffer-copy-paste.test.ts", {"duration": 50.02589999999998, "failed": false}], [":tests/exec-apply-patch.test.ts", {"duration": 17.322000000000003, "failed": false}], [":tests/requires-shell.test.ts", {"duration": 14.986800000000017, "failed": false}], [":tests/input-utils.test.ts", {"duration": 19.53989999999999, "failed": false}], [":tests/terminal-chat-response-item.test.tsx", {"duration": 81.93870000000015, "failed": false}], [":tests/multiline-shift-enter-crlf.test.tsx", {"duration": 96.1105, "failed": false}], [":tests/disableResponseStorage.test.ts", {"duration": 32.36810000000003, "failed": false}], [":tests/slash-commands.test.ts", {"duration": 12.917599999999993, "failed": false}], [":tests/multiline-shift-enter-mod1.test.tsx", {"duration": 89.04280000000017, "failed": false}], [":tests/fixed-requires-shell.test.ts", {"duration": 9.38530000000003, "failed": false}], [":tests/parse-apply-patch.test.ts", {"duration": 10.712800000000016, "failed": false}], [":tests/multiline-shift-enter.test.tsx", {"duration": 77.6223, "failed": false}], [":tests/terminal-chat-input-compact.test.tsx", {"duration": 52.8143, "failed": false}], [":tests/multiline-ctrl-enter-submit.test.tsx", {"duration": 83.25510000000008, "failed": false}], [":tests/api-key.test.ts", {"duration": 57.31700000000001, "failed": false}], [":tests/multiline-enter-submit-cr.test.tsx", {"duration": 64.91339999999991, "failed": false}], [":tests/get-diff-special-chars.test.ts", {"duration": 824.7857000000001, "failed": false}], [":tests/format-command.test.ts", {"duration": 7.77030000000002, "failed": false}], [":tests/model-info.test.ts", {"duration": 12.504300000000057, "failed": false}], [":tests/pipe-command.test.ts", {"duration": 7.362799999999993, "failed": false}], [":tests/text-buffer-crlf.test.ts", {"duration": 29.532799999999952, "failed": false}], [":tests/dummy.test.ts", {"duration": 5.052999999999997, "failed": false}]]}